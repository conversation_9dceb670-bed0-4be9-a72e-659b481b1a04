amulety --help

mkdir tutorial
wget -P tutorial https://zenodo.org/records/11373741/files/AIRR_subject1_FNA_d0_1_Y1.tsv
wget -P tutorial -c https://github.com/nf-core/test-datasets/raw/airrflow/database-cache/igblast_base.zip
unzip tutorial/igblast_base.zip -d tutorial
rm tutorial/igblast_base.zip

amulety translate-igblast tutorial/AIRR_subject1_FNA_d0_1_Y1.tsv tutorial tutorial/igblast_base

amulety antiberty tutorial/AIRR_subject1_FNA_d0_1_Y1_translated.tsv HL tutorial/AIRR_subject1_FNA_d0_1_Y1_antiBERTy.tsv

amulety antiberta2 tutorial/AIRR_subject1_FNA_d0_1_Y1_translated.tsv HL tutorial/AIRR_subject1_FNA_d0_1_Y1_antiBERTa2.tsv

amulety esm2 tutorial/AIRR_subject1_FNA_d0_1_Y1_translated.tsv HL tutorial/AIRR_subject1_FNA_d0_1_Y1_esm2.tsv

wget -P tutorial https://zenodo.org/records/8237396/files/BALM-paired.tar.gz
tar -xzf tutorial/BALM-paired.tar.gz -C tutorial
rm tutorial/BALM-paired.tar.gz

amulety custommodel tutorial/BALM-paired_LC-coherence_90-5-5-split_122222 \\
 tutorial/AIRR_subject1_FNA_d0_1_Y1_translated.tsv HL \\
 tutorial/AIRR_subject1_FNA_d0_1_Y1_BALM-paired.tsv \\
 --embedding-dimension 1024 \\
 --batch-size 25 \\
 --max-length 510