{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# AMULETY CLI Tutorial"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Introduction\n", "\n", "This tutorial demonstrates how to use AMULETY command line interface (CLI) to translate and embed BCR sequences. Before getting started, please install AMULETY using `pip install amulety`. You can check available commands from AMULETY by running `amulety --help`. "]}, {"cell_type": "code", "execution_count": 1, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " █████  ███    ███ ██    ██ ██      ███████ ████████     ██    ██\n", "██   ██ ████  ████ ██    ██ ██      ██         ██         ██  ██\n", "███████ ██ ████ ██ ██    ██ ██      █████      ██          ████\n", "██   ██ ██  ██  ██ ██    ██ ██      ██         ██           ██\n", "██   ██ ██      ██  ██████  ███████ ███████    ██           ██\n", "\n", "AMULETY: Adaptive imMUne receptor Language model Embedding Tool\n", " version \u001b[1;36m1.0\u001b[0m\n", "\n", "\u001b[1m                                                                                \u001b[0m\n", "\u001b[1m \u001b[0m\u001b[1;33mUsage: \u001b[0m\u001b[1mamulety [OPTIONS] COMMAND [ARGS]...\u001b[0m\u001b[1m                                    \u001b[0m\u001b[1m \u001b[0m\n", "\u001b[1m                                                                                \u001b[0m\n", "\u001b[2m╭─\u001b[0m\u001b[2m Op<PERSON> \u001b[0m\u001b[2m───────────────────────────────────────────────────────────────────\u001b[0m\u001b[2m─╮\u001b[0m\n", "\u001b[2m│\u001b[0m \u001b[1;36m-\u001b[0m\u001b[1;36m-install\u001b[0m\u001b[1;36m-completion\u001b[0m          Install completion for the current shell.      \u001b[2m│\u001b[0m\n", "\u001b[2m│\u001b[0m \u001b[1;36m-\u001b[0m\u001b[1;36m-show\u001b[0m\u001b[1;36m-completion\u001b[0m             Show completion for the current shell, to copy \u001b[2m│\u001b[0m\n", "\u001b[2m│\u001b[0m                               it or customize the installation.              \u001b[2m│\u001b[0m\n", "\u001b[2m│\u001b[0m \u001b[1;36m-\u001b[0m\u001b[1;36m-help\u001b[0m                        Show this message and exit.                    \u001b[2m│\u001b[0m\n", "\u001b[2m╰──────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n", "\u001b[2m╭─\u001b[0m\u001b[2m Commands \u001b[0m\u001b[2m──────────────────────────────────────────────────────────────────\u001b[0m\u001b[2m─╮\u001b[0m\n", "\u001b[2m│\u001b[0m \u001b[1;36mantiberta2        \u001b[0m\u001b[1;36m \u001b[0m Embeds sequences using the antiBERTa2 RoFormer model.    \u001b[2m│\u001b[0m\n", "\u001b[2m│\u001b[0m \u001b[1;36mantiberty         \u001b[0m\u001b[1;36m \u001b[0m Embeds sequences using the AntiBERTy model.              \u001b[2m│\u001b[0m\n", "\u001b[2m│\u001b[0m \u001b[1;36mcustommodel       \u001b[0m\u001b[1;36m \u001b[0m This function generates embeddings for a given dataset   \u001b[2m│\u001b[0m\n", "\u001b[2m│\u001b[0m \u001b[1;36m                   \u001b[0m using a pretrained model. The function first checks if a \u001b[2m│\u001b[0m\n", "\u001b[2m│\u001b[0m \u001b[1;36m                   \u001b[0m CUDA device is available for PyTorch to use. It then     \u001b[2m│\u001b[0m\n", "\u001b[2m│\u001b[0m \u001b[1;36m                   \u001b[0m loads the data from the input file and preprocesses it.  \u001b[2m│\u001b[0m\n", "\u001b[2m│\u001b[0m \u001b[1;36m                   \u001b[0m The sequences are tokenized and fed into the pretrained  \u001b[2m│\u001b[0m\n", "\u001b[2m│\u001b[0m \u001b[1;36m                   \u001b[0m model to generate embeddings. The embeddings are then    \u001b[2m│\u001b[0m\n", "\u001b[2m│\u001b[0m \u001b[1;36m                   \u001b[0m saved to the specified output path.                      \u001b[2m│\u001b[0m\n", "\u001b[2m│\u001b[0m \u001b[1;36mesm2              \u001b[0m\u001b[1;36m \u001b[0m Embeds sequences using the ESM2 model.                   \u001b[2m│\u001b[0m\n", "\u001b[2m│\u001b[0m \u001b[1;36mtranslate-igblast \u001b[0m\u001b[1;36m \u001b[0m Translates nucleotide sequences to amino acid sequences  \u001b[2m│\u001b[0m\n", "\u001b[2m│\u001b[0m \u001b[1;36m                   \u001b[0m using IgBlast.                                           \u001b[2m│\u001b[0m\n", "\u001b[2m╰──────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n", "\n"]}], "source": ["amulety --help"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Translating nucleotides to amino acid sequences\n", "\n", "The inputs to the embedding models are [AIRR format file](https://docs.airr-community.org/en/stable/datarep/overview.html#datarepresentations) with antibody amino acid sequences. If the AIRR file only contains nucleotide sequences, `amulety translate-igblast` command can help with the translation. The input requires\n", "- Path to the V(D)J sequence AIRR file\n", "- Output directory path to write the translated sequences\n", "- Reference IgBlast database to perform alignment and translation\n", "\n", "The following command downloads an example AIRR format file and the reference IgBlast database. "]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["mkdir tutorial\n", "wget -P tutorial https://zenodo.org/records/11373741/files/AIRR_subject1_FNA_d0_1_Y1.tsv\n", "wget -P tutorial -c https://github.com/nf-core/test-datasets/raw/airrflow/database-cache/igblast_base.zip\n", "unzip tutorial/igblast_base.zip -d tutorial\n", "rm tutorial/igblast_base.zip"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now we are ready to run the translation command as follows. "]}, {"cell_type": "code", "execution_count": 7, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " █████  ███    ███ ██    ██ ██      ███████ ████████     ██    ██\n", "██   ██ ████  ████ ██    ██ ██      ██         ██         ██  ██\n", "███████ ██ ████ ██ ██    ██ ██      █████      ██          ████\n", "██   ██ ██  ██  ██ ██    ██ ██      ██         ██           ██\n", "██   ██ ██      ██  ██████  ███████ ███████    ██           ██\n", "\n", "AMULETY: Adaptive imMUne receptor Language model Embedding Tool\n", " version \u001b[1;36m1.0\u001b[0m\n", "\n", "2024-06-06 13:50:25,146 - INFO - Converting AIRR table to FastA for IgBlast translation...\n", "2024-06-06 13:50:25,156 - INFO - Calling IgBlast for running translation...\n", "2024-06-06 13:50:33,793 - INFO - Saved the translations in the dataframe (sequence_aa contains the full translation and sequence_vdj_aa contains the VDJ translation).\n", "2024-06-06 13:50:33,795 - INFO - Saved the translations in tutorial/AIRR_subject1_FNA_d0_1_Y1_translated.tsv file.\n", "2024-06-06 13:50:33,815 - INFO - <PERSON><PERSON> 8.67 seconds\n"]}], "source": ["amulety translate-igblast tutorial/AIRR_subject1_FNA_d0_1_Y1.tsv tutorial tutorial/igblast_base"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The command outputs an AIRR file with three new columns appended to the original data table:\n", "- `sequence_aa`: the whole translated sequence\n", "\n", "- `sequence_vdj_aa`: the translated sequence part of VDJ (excluding constant region)\n", "\n", "- `sequence_alignment_aa`: the translated sequence part of VDJ with gaps annotated as - when there are amino acid deletions."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Embeddings\n", "\n", "Now we are ready to embed the seequences. AMULETY currently supports three published pre-trained model (antiberty, antiBERTa2, ESM2-650M) as well as weights from customized pre-trained model from the huggingface framework.\n", "\n", "\n", "### Published pre-trained models\n", "\n", "The input arguments for the published models include:\n", "\n", "* `input_file_path`: Path to the input AIRR file containing the translated columns\n", "\n", "* `chain`: Chain(s) to embed: heavy only (H), light only (L), heavy and light concatenated per cell barcode (HL)\n", "\n", "* `output_file_path`: Path to the output embedding matrix for corresponding chain(s). We currently support file extension csv, tsv, pt. csv and tsv file contains the cell barcode and/or sequence ID as indices. pt file, which is saved by `torch.save`, doesn't contain index but the order will be maintained as the original data for H and L option.\n", "\n", "Optional arguments include:\n", "\n", "* `sequence-col`: the column to the amino acid sequence (default is `sequence_vdj_aa`)\n", "\n", "* `cell-id-col`: the column to the single-cell barcode (default is `cell_id`)\n", "\n", "* `batch-size`: the mini-batch size for embedding the sequences. \n", "\n", "The package will auto-detect GPU and use GPU when it is available. Note that `batch-size` parameter can be adjusted to avoid GPU out-of-memory error. \n", "\n", "The output file containing the embeddings will be written as specified by `output_file_path`. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### AntiBERTy"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " █████  ███    ███ ██    ██ ██      ███████ ████████     ██    ██\n", "██   ██ ████  ████ ██    ██ ██      ██         ██         ██  ██\n", "███████ ██ ████ ██ ██    ██ ██      █████      ██          ████\n", "██   ██ ██  ██  ██ ██    ██ ██      ██         ██           ██\n", "██   ██ ██      ██  ██████  ███████ ███████    ██           ██\n", "\n", "AMULETY: Adaptive imMUne receptor Language model Embedding Tool\n", " version \u001b[1;36m1.0\u001b[0m\n", "\n", "2024-06-06 14:24:48,605 - INFO - Processing single-cell BCR data...\n", "2024-06-06 14:24:48,605 - INFO - Concatenating heavy and light chain per cell...\n", "2024-06-06 14:24:48,626 - INFO - Embedding 95 sequences using antiberty...\n", "2024-06-06 14:24:49,541 - INFO - AntiBERTy loaded. Size: 26.03 M\n", "2024-06-06 14:24:49,541 - INFO - Batch 1/1\n", "/home/<USER>/.conda/envs/bcrembed/lib/python3.12/site-packages/torch/cuda/__init__.py:619: UserWarning: Can't initialize NVML\n", "  warnings.warn(\"Can't initialize NVML\")\n", "2024-06-06 14:25:11,820 - INFO - <PERSON><PERSON> 22.28 seconds\n", "2024-06-06 14:25:11,883 - INFO - Saved embedding at tutorial/AIRR_subject1_FNA_d0_1_Y1_antiBERTy.tsv\n"]}], "source": ["amulety antiberty tutorial/AIRR_subject1_FNA_d0_1_Y1_translated.tsv HL tutorial/AIRR_subject1_FNA_d0_1_Y1_antiBERTy.tsv"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### AntiBERTa2"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " █████  ███    ███ ██    ██ ██      ███████ ████████     ██    ██\n", "██   ██ ████  ████ ██    ██ ██      ██         ██         ██  ██\n", "███████ ██ ████ ██ ██    ██ ██      █████      ██          ████\n", "██   ██ ██  ██  ██ ██    ██ ██      ██         ██           ██\n", "██   ██ ██      ██  ██████  ███████ ███████    ██           ██\n", "\n", "AMULETY: Adaptive imMUne receptor Language model Embedding Tool\n", " version \u001b[1;36m1.0\u001b[0m\n", "\n", "2024-06-06 14:25:23,884 - INFO - Processing single-cell BCR data...\n", "2024-06-06 14:25:23,884 - INFO - Concatenating heavy and light chain per cell...\n", "/home/<USER>/.conda/envs/bcrembed/lib/python3.12/site-packages/huggingface_hub/file_download.py:1132: FutureWarning: `resume_download` is deprecated and will be removed in version 1.0.0. Downloads always resume when possible. If you want to force a new download, use `force_download=True`.\n", "  warnings.warn(\n", "model.safetensors: 100%|██████████████████████| 811M/811M [00:02<00:00, 314MB/s]\n", "2024-06-06 14:25:31,000 - INFO - AntiBERTa2 loaded. Size: 202.642462 M\n", "2024-06-06 14:25:31,000 - INFO - Batch 1/1.\n", "/home/<USER>/.conda/envs/bcrembed/lib/python3.12/site-packages/torch/cuda/__init__.py:619: UserWarning: Can't initialize NVML\n", "  warnings.warn(\"Can't initialize NVML\")\n", "2024-06-06 14:28:01,505 - INFO - <PERSON><PERSON> 150.5 seconds\n", "2024-06-06 14:28:01,602 - INFO - Saved embedding at tutorial/AIRR_subject1_FNA_d0_1_Y1_antiBERTa2.tsv\n"]}], "source": ["amulety antiberta2 tutorial/AIRR_subject1_FNA_d0_1_Y1_translated.tsv HL tutorial/AIRR_subject1_FNA_d0_1_Y1_antiBERTa2.tsv"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### ESM2-650M"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " █████  ███    ███ ██    ██ ██      ███████ ████████     ██    ██\n", "██   ██ ████  ████ ██    ██ ██      ██         ██         ██  ██\n", "███████ ██ ████ ██ ██    ██ ██      █████      ██          ████\n", "██   ██ ██  ██  ██ ██    ██ ██      ██         ██           ██\n", "██   ██ ██      ██  ██████  ███████ ███████    ██           ██\n", "\n", "AMULETY: Adaptive imMUne receptor Language model Embedding Tool\n", " version \u001b[1;36m1.0\u001b[0m\n", "\n", "2024-06-06 14:28:32,409 - INFO - Processing single-cell BCR data...\n", "2024-06-06 14:28:32,409 - INFO - Concatenating heavy and light chain per cell...\n", "2024-06-06 14:28:44,899 - INFO - ESM2 650M model size: 652.36 M\n", "2024-06-06 14:28:44,903 - INFO - <PERSON><PERSON> 1/2.\n", "/home/<USER>/.conda/envs/bcrembed/lib/python3.12/site-packages/torch/cuda/__init__.py:619: UserWarning: Can't initialize NVML\n", "  warnings.warn(\"Can't initialize NVML\")\n"]}], "source": ["amulety esm2 tutorial/AIRR_subject1_FNA_d0_1_Y1_translated.tsv HL tutorial/AIRR_subject1_FNA_d0_1_Y1_esm2.tsv"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Pre-trained weights of customized model\n", "\n", "We will download the [pre-trained weights](https://zenodo.org/records/8237396/files/BALM-paired.tar.gz) from [BALM-paired model](https://www.sciencedirect.com/science/article/pii/S2666389924000758?via%3Dihub). "]}, {"cell_type": "code", "execution_count": 15, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--2024-06-06 14:54:13--  https://zenodo.org/records/8237396/files/BALM-paired.tar.gz\n", "Resolving zenodo.org (zenodo.org)... ***************, **************, **************, ...\n", "Connecting to zenodo.org (zenodo.org)|***************|:443... connected.\n", "HTTP request sent, awaiting response... 200 OK\n", "Length: 1129993036 (1.1G) [application/octet-stream]\n", "Saving to: ‘tutorial/BALM-paired.tar.gz.1’\n", "\n", "BALM-paired.tar.gz. 100%[===================>]   1.05G  37.8MB/s    in 26s     \n", "\n", "2024-06-06 14:54:40 (41.3 MB/s) - ‘tutorial/BALM-paired.tar.gz.1’ saved [1129993036/1129993036]\n", "\n"]}], "source": ["wget -P tutorial https://zenodo.org/records/8237396/files/BALM-paired.tar.gz\n", "tar -xzf tutorial/BALM-paired.tar.gz -C tutorial\n", "rm tutorial/BALM-paired.tar.gz"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In addition to the parameters mentioned above, we need to specify the following parameters:\n", "\n", "* `modelpath`: the path to the downloaded model weights\n", "\n", "* `embedding-dimension`: the dimension of the embedding\n", "\n", "* `max-length`: maximum length taken by the model"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " █████  ███    ███ ██    ██ ██      ███████ ████████     ██    ██\n", "██   ██ ████  ████ ██    ██ ██      ██         ██         ██  ██\n", "███████ ██ ████ ██ ██    ██ ██      █████      ██          ████\n", "██   ██ ██  ██  ██ ██    ██ ██      ██         ██           ██\n", "██   ██ ██      ██  ██████  ███████ ███████    ██           ██\n", "\n", "AMULETY: Adaptive imMUne receptor Language model Embedding Tool\n", " version \u001b[1;36m1.0\u001b[0m\n", "\n", "2024-06-06 15:21:05,068 - INFO - Processing single-cell BCR data...\n", "2024-06-06 15:21:05,068 - INFO - Concatenating heavy and light chain per cell...\n", "2024-06-06 15:21:07,869 - INFO - Model size: 303.92M\n", "Batch 1/4\n", "\n", "huggingface/tokenizers: The current process just got forked, after parallelism has already been used. Disabling parallelism to avoid deadlocks...\n", "To disable this warning, you can either:\n", "\t- Avoid using `tokenizers` before the fork if possible\n", "\t- Explicitly set the environment variable TOKENIZERS_PARALLELISM=(true | false)\n", "huggingface/tokenizers: The current process just got forked, after parallelism has already been used. Disabling parallelism to avoid deadlocks...\n", "To disable this warning, you can either:\n", "\t- Avoid using `tokenizers` before the fork if possible\n", "\t- Explicitly set the environment variable TOKENIZERS_PARALLELISM=(true | false)\n", "huggingface/tokenizers: The current process just got forked, after parallelism has already been used. Disabling parallelism to avoid deadlocks...\n", "To disable this warning, you can either:\n", "\t- Avoid using `tokenizers` before the fork if possible\n", "\t- Explicitly set the environment variable TOKENIZERS_PARALLELISM=(true | false)\n", "/home/<USER>/.conda/envs/bcrembed/lib/python3.12/site-packages/torch/cuda/__init__.py:619: UserWarning: Can't initialize NVML\n", "  warnings.warn(\"Can't initialize NVML\")\n", "Batch 2/4\n", "\n", "Batch 3/4\n", "\n", "Batch 4/4\n", "\n", "2024-06-06 15:28:50,302 - INFO - <PERSON><PERSON> 462.43 seconds\n", "2024-06-06 15:28:50,423 - INFO - Saved embedding at tutorial/AIRR_subject1_FNA_d0_1_Y1_BALM-paired.tsv\n"]}], "source": ["amulety custommodel tutorial/BALM-paired_LC-coherence_90-5-5-split_122222 \\\\\n", " tutorial/AIRR_subject1_FNA_d0_1_Y1_translated.tsv HL \\\\\n", " tutorial/AIRR_subject1_FNA_d0_1_Y1_BALM-paired.tsv \\\\\n", " --embedding-dimension 1024 \\\\\n", " --batch-size 25 \\\\\n", " --max-length 510"]}], "metadata": {"kernelspec": {"display_name": "amulety", "language": "python", "name": "amulety"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}