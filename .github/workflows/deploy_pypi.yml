name: Publish amulety to PyPI
on:
  release:
    types: [published]

# Cancel if a newer run is started
concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  build-n-publish:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@0ad4b8fadaa221de15dcec353f45205ec38ea70b # v4
        name: Check out source-code repository

      - name: Set up Python 3.12
        uses: actions/setup-python@82c7e631bb3cdc910f68e0081d67478d79c6982d # v5
        with:
          python-version: "3.12"

      - name: Install python dependencies
        run: |
          python -m pip install --upgrade pip setuptools wheel
          pip install .

      - name: Build the distribution
        run: python setup.py sdist bdist_wheel

      - name: Publish dist to PyPI
        if: github.repository == 'immcantation/amulety'
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          password: ${{ secrets.pypi_password }}
