name: "Check docs"
on:
  - pull_request
  - push

jobs:
  docs:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
          python-version: "3.11"
          architecture: "x64"
      - name: Install Pandoc
        run: |
          sudo apt-get install pandoc
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install -r requirements_dev.txt
      - name: check docs
        run: |
          cd ${GITHUB_WORKSPACE}/docs
          make html
