name: Python package

on:
  push:
  pull_request:
    branches:
      - main
      - dev

# Cancel if a newer run is started
concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.8", "3.9", "3.10", "3.11", "3.12"]

    steps:
      - uses: actions/checkout@v4
      - name: Setup Python # Set Python version
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Setup conda
        uses: conda-incubator/setup-miniconda@v2
        with:
          miniconda-version: "latest"
          activate-environment: foo
      # Install pip and pytest
      - name: Install dependencies
        shell: bash -el {0}
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install -e .
      - name: Test with pytest
        shell: bash -el {0}
        run: pytest --doctest-modules --junitxml=junit/test-results-${{ matrix.python-version }}.xml
      - name: Upload pytest test results
        uses: actions/upload-artifact@v4
        with:
          name: pytest-results-${{ matrix.python-version }}
          path: junit/test-results-${{ matrix.python-version }}.xml
        # Use always() to always run this step to publish test results when there are test failures
        if: ${{ always() }}
      - name: Test IgBlast translation
        shell: bash -el {0}
        run: |
          conda install -y -c conda-forge -c bioconda -c defaults igblast=1.22.0
          wget -c https://github.com/nf-core/test-datasets/raw/airrflow/database-cache/igblast_base.zip
          unzip igblast_base.zip
          amulety translate-igblast tests/AIRR_rearrangement_single-cell_testtranslation.tsv . igblast_base
