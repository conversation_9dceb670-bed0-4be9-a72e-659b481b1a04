name: Lint tools code formatting
on:
  push:
    branches:
      - dev
      - main
  pull_request:
  release:
    types: [published]

# Cancel if a newer run is started
concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  Pre-commit:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@0ad4b8fadaa221de15dcec353f45205ec38ea70b # v4

      - name: Set up Python 3.12
        uses: actions/setup-python@82c7e631bb3cdc910f68e0081d67478d79c6982d # v5
        with:
          python-version: "3.12"
          cache: "pip"

      - name: Install pre-commit
        run: pip install pre-commit

      - name: Run pre-commit
        run: pre-commit run --all-files
