[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"


[tool.pytest.ini_options]
markers = ["datafiles: load datafiles"]
testpaths = ["tests"]
norecursedirs = [
    ".*",
    "build",
    "dist",
    "*.egg",
    "data",
    "__pycache__",
    ".github",
    "src",
    "docs",
]


[tool.ruff]
line-length = 120
target-version = "py38"
cache-dir = "~/.cache/ruff"

[tool.ruff.lint]
select = ["I", "E1", "E4", "E7", "E9", "F", "UP", "N"]

[tool.ruff.lint.isort]
known-first-party = ["amulety"]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["E402", "F401"]
"tests/*" = ["N802"]
"amulety.py" = ["N806"]

[tool.ruff.lint.pep8-naming]
extend-ignore-names = ["mocked_*", "*allOf", "*URI*"]
