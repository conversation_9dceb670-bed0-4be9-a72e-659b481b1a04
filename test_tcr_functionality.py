#!/usr/bin/env python3
"""
Test script to verify TCR functionality in Amulety
"""

import sys
import os
import pandas as pd
import tempfile

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(__file__))

# Import directly from utils module
from amulety.utils import process_tcr_airr, concatenate_alphabeta

def create_test_tcr_data():
    """Create a simple test TCR dataset"""
    data = {
        'sequence_id': ['tcr_001', 'tcr_002', 'tcr_003', 'tcr_004'],
        'cell_id': ['cell_1', 'cell_1', 'cell_2', 'cell_2'],
        'locus': ['TRA', 'TRB', 'TRA', 'TRB'],
        'v_call': ['TRAV1*01', 'TRBV1*01', 'TRAV2*01', 'TRBV2*01'],
        'sequence_vdj_aa': [
            'QVQLVQSGAEVKKPGASVKVSCKASGYTFTSYAMHWVRQAPGQRLEWMG',
            'EVQLVESGGGLVQPGGSLRLSCAASGFTFSSYAMSWVRQAPGKGLEWVS',
            'QSALTQPASVSGSPGQSITISCTGTSSDVGGYNYVSWYQQHPGKAPKLM',
            'DIQMTQSPSSLSASVGDRVTITCRASQGISNSLAWFQQKPGKAPKLLLY'
        ],
        'duplicate_count': [10, 15, 8, 12]
    }

    df = pd.DataFrame(data)
    return df

def test_tcr_processing():
    """Test TCR data processing functions"""
    print("=== Testing TCR Data Processing ===")

    # Create test data
    test_data = create_test_tcr_data()

    # Save to temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.tsv', delete=False) as f:
        test_data.to_csv(f.name, sep='\t', index=False)
        temp_file = f.name

    try:
        # Test alpha chain processing
        print("Testing alpha chain (A) processing...")
        alpha_data = process_tcr_airr(temp_file, "A")
        print(f"Alpha chains found: {alpha_data.shape[0]}")
        print(alpha_data.head())

        # Test beta chain processing
        print("\nTesting beta chain (B) processing...")
        beta_data = process_tcr_airr(temp_file, "B")
        print(f"Beta chains found: {beta_data.shape[0]}")
        print(beta_data.head())

        # Test alpha-beta concatenation
        print("\nTesting alpha-beta (AB) concatenation...")
        ab_data = process_tcr_airr(temp_file, "AB")
        print(f"Alpha-beta pairs found: {ab_data.shape[0]}")
        print(ab_data.head())

        print("\n=== TCR Processing Test Completed Successfully! ===")

    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # Clean up
        os.unlink(temp_file)

def test_command_availability():
    """Test if TCR commands are available"""
    print("\n=== Testing Command Availability ===")

    try:
        from amulety.amulety import tcr_prott5, tcr_esm2
        print("✓ TCR commands imported successfully")
        print("✓ tcr_prott5 command available")
        print("✓ tcr_esm2 command available")
    except ImportError as e:
        print(f"✗ Error importing TCR commands: {e}")

if __name__ == "__main__":
    print("Testing TCR functionality in Amulety...")
    test_tcr_processing()
    test_command_availability()
    print("\nAll tests completed!")
