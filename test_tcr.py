import pandas as pd

real_tcr_data = {
    'sequence_id': ['tcr_001', 'tcr_002', 'tcr_003', 'tcr_004'],
    'cell_id': ['cell_1', 'cell_1', 'cell_2', 'cell_2'],
    'locus': ['TRA', 'TRB', 'TRA', 'TRB'],
    'v_call': ['TRAV1*01', 'TRBV1*01', 'TRAV2*01', 'TRBV2*01'],
    'sequence_vdj_aa': [
        'QVQLVQSGAEVKKPGASVKVSCKASGYTFTSYAMHWVRQAPGQRLEWMG',  # real α chain    
        'EVQLVESGGGLVQPGGSLRLSCAASGFTFSSYAMSWVRQAPGKGLEWVS',  # real β chain
        'QSALTQPASVSGSPGQSITISCTGTSSDVGGYNYVSWYQQHPGKAPKLM',
        'DIQMTQSPSSLSASVGDRVTITCRASQGISNSLAWFQQKPGKAPKLLLY'
    ],
    'duplicate_count': [10, 15, 8, 12]
}

df = pd.DataFrame(real_tcr_data)
df.to_csv('test_tcr_data.tsv', sep='\t', index=False)